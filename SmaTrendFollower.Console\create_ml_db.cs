using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Data;

namespace SmaTrendFollower.Console;

/// <summary>
/// Simple utility to create the ML features database
/// </summary>
public static class CreateMLDatabase
{
    public static async Task CreateAsync()
    {
        System.Console.WriteLine("Creating ML Features database...");

        var options = new DbContextOptionsBuilder<MLFeaturesDbContext>()
            .UseNpgsql("Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;")
            .Options;

        using var context = new MLFeaturesDbContext(options);

        // Create the database and tables
        await context.Database.EnsureCreatedAsync();

        System.Console.WriteLine("✅ ML Features database created successfully!");
        System.Console.WriteLine("Database file: ml_features.db");
        System.Console.WriteLine("Tables created: Features, FillsLog");
    }
}
