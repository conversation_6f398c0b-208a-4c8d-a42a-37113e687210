using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using EFCore.BulkExtensions;

namespace SmaTrendFollower.Data;

/// <summary>
/// Entity Framework DbContext for SQLite stock bar caching.
/// Manages cached stock/ETF data to reduce API calls to Alpaca/Polygon.
/// Implements 1-year retention with efficient indexing.
/// </summary>
public class StockBarCacheDbContext : DbContext
{
    private readonly ILogger<StockBarCacheDbContext>? _logger;

    public StockBarCacheDbContext(DbContextOptions<StockBarCacheDbContext> options, ILogger<StockBarCacheDbContext>? logger = null) : base(options)
    {
        _logger = logger;
    }

    /// <summary>
    /// Cached stock/ETF bars from Alpaca/Polygon APIs
    /// </summary>
    public DbSet<CachedStockBar> CachedStockBars { get; set; } = null!;

    /// <summary>
    /// Metadata about cached data for each symbol and timeframe
    /// </summary>
    public DbSet<StockCacheMetadata> StockCacheMetadata { get; set; } = null!;

    /// <summary>
    /// Historical trailing stop levels for positions
    /// </summary>
    public DbSet<TrailingStopRecord> TrailingStops { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure CachedStockBar
        modelBuilder.Entity<CachedStockBar>(entity =>
        {
            // Composite unique index for efficient queries and deduplication
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame, e.TimeUtc })
                  .IsUnique()
                  .HasDatabaseName("IX_CachedStockBars_Symbol_TimeFrame_TimeUtc");

            // Additional indexes for common query patterns
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame })
                  .HasDatabaseName("IX_CachedStockBars_Symbol_TimeFrame");

            entity.HasIndex(e => e.TimeUtc)
                  .HasDatabaseName("IX_CachedStockBars_TimeUtc");

            entity.HasIndex(e => e.CachedAt)
                  .HasDatabaseName("IX_CachedStockBars_CachedAt");

            // Financial data precision: 18 digits total, 4 decimal places for accurate calculations
            entity.Property(e => e.Open).HasPrecision(18, 4);
            entity.Property(e => e.High).HasPrecision(18, 4);
            entity.Property(e => e.Low).HasPrecision(18, 4);
            entity.Property(e => e.Close).HasPrecision(18, 4);
            entity.Property(e => e.Vwap).HasPrecision(18, 4);

            // Volume precision: 18 digits total, 0 decimal places (whole shares)
            entity.Property(e => e.Volume).HasPrecision(18, 0);
        });

        // Configure StockCacheMetadata
        modelBuilder.Entity<StockCacheMetadata>(entity =>
        {
            entity.HasKey(e => e.CacheKey);

            entity.HasIndex(e => new { e.Symbol, e.TimeFrame })
                  .HasDatabaseName("IX_StockCacheMetadata_Symbol_TimeFrame");
        });

        // Configure TrailingStopRecord
        modelBuilder.Entity<TrailingStopRecord>(entity =>
        {
            // Composite unique index for efficient queries and deduplication
            entity.HasIndex(e => new { e.Symbol, e.Date })
                  .IsUnique()
                  .HasDatabaseName("IX_TrailingStops_Symbol_Date");

            // Additional indexes for common query patterns
            entity.HasIndex(e => e.Symbol)
                  .HasDatabaseName("IX_TrailingStops_Symbol");

            entity.HasIndex(e => e.Date)
                  .HasDatabaseName("IX_TrailingStops_Date");

            entity.HasIndex(e => e.IsActive)
                  .HasDatabaseName("IX_TrailingStops_IsActive");

            entity.HasIndex(e => e.CreatedAt)
                  .HasDatabaseName("IX_TrailingStops_CreatedAt");

            // Financial data precision: 18 digits total, 4 decimal places for accurate calculations
            entity.Property(e => e.StopPrice).HasPrecision(18, 4);
            entity.Property(e => e.HighWaterMark).HasPrecision(18, 4);
            entity.Property(e => e.Atr).HasPrecision(18, 4);
            entity.Property(e => e.EntryPrice).HasPrecision(18, 4);

            // Quantity precision: 18 digits total, 0 decimal places (whole shares)
            entity.Property(e => e.Quantity).HasPrecision(18, 0);
        });
    }

    /// <summary>
    /// Ensures the database is created and migrations are applied with optimization
    /// </summary>
    public async Task EnsureDatabaseCreatedAsync()
    {
        var created = await Database.EnsureCreatedAsync();
        if (created)
        {
            _logger?.LogInformation("Database created successfully");

            // Apply initial optimizations for new database
            await ApplyInitialOptimizationsAsync();
        }
    }

    /// <summary>
    /// Applies initial database optimizations for new databases
    /// </summary>
    private async Task ApplyInitialOptimizationsAsync()
    {
        try
        {
            // PostgreSQL-specific optimizations (no SQLite PRAGMA commands)
            var optimizationCommands = new[]
            {
                // Set work memory for complex queries
                "SET work_mem = '256MB';",
                // Set shared buffers for better caching
                "SET shared_buffers = '256MB';",
                // Enable parallel query execution
                "SET max_parallel_workers_per_gather = 4;",
                // Optimize for read-heavy workloads
                "SET random_page_cost = 1.1;"
            };

            foreach (var command in optimizationCommands)
            {
                await Database.ExecuteSqlRawAsync(command);
            }

            _logger?.LogDebug("Applied PostgreSQL database optimizations");
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to apply some PostgreSQL optimizations: {Error}", ex.Message);
        }
    }

    /// <summary>
    /// Gets cached bars for a symbol and timeframe within a date range
    /// </summary>
    public async Task<List<CachedStockBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        try
        {
            // Validate inputs before database query
            if (string.IsNullOrWhiteSpace(symbol) || string.IsNullOrWhiteSpace(timeFrame))
            {
                _logger?.LogWarning("Invalid parameters for GetCachedBarsAsync: Symbol='{Symbol}', TimeFrame='{TimeFrame}'", symbol, timeFrame);
                return new List<CachedStockBar>();
            }

            if (startDate > endDate)
            {
                _logger?.LogWarning("Invalid date range for {Symbol} {TimeFrame}: startDate {StartDate} > endDate {EndDate}",
                    symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));
                return new List<CachedStockBar>();
            }

            return await CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame && b.TimeUtc >= startDate && b.TimeUtc <= endDate)
                .OrderBy(b => b.TimeUtc)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Database error in GetCachedBarsAsync for {Symbol} {TimeFrame}: {ErrorMessage}", symbol, timeFrame, ex.Message);
            return new List<CachedStockBar>();
        }
    }

    /// <summary>
    /// Gets the latest cached date for a symbol and timeframe
    /// </summary>
    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame)
    {
        var cacheKey = Models.StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var metadata = await this.StockCacheMetadata.FindAsync(cacheKey);
        return metadata?.LatestDataDate;
    }

    /// <summary>
    /// Gets the earliest cached date for a symbol and timeframe
    /// </summary>
    public async Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame)
    {
        var cacheKey = Models.StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var metadata = await this.StockCacheMetadata.FindAsync(cacheKey);
        return metadata?.EarliestDataDate;
    }

    /// <summary>
    /// Adds or updates cached bars for a symbol and timeframe using PostgreSQL UPSERT for maximum performance
    /// </summary>
    public async Task AddOrUpdateCachedBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();

        if (!cachedBars.Any())
            return;

        // Basic validation to ensure we have valid data
        if (cachedBars.Any(bar => bar.TimeUtc == default))
        {
            _logger?.LogWarning("Invalid timestamp detected for {Symbol} {TimeFrame}, skipping cache operation", symbol, timeFrame);
            return;
        }

        try
        {

        // Check if we're using in-memory database (transactions not supported)
        var isInMemory = Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

        if (isInMemory)
        {
            await AddOrUpdateCachedBarsWithoutTransactionAsync(symbol, timeFrame, cachedBars);
        }
        else
        {
            // Check if we're already in a transaction to avoid nested transactions
            if (Database.CurrentTransaction != null)
            {
                // Already in a transaction, just execute without creating a new one
                await AddOrUpdateCachedBarsWithoutTransactionAsync(symbol, timeFrame, cachedBars);
            }
            else
            {
                // Not in a transaction, create one
                using var transaction = await Database.BeginTransactionAsync();
                try
                {
                    await AddOrUpdateCachedBarsWithoutTransactionAsync(symbol, timeFrame, cachedBars);
                    await transaction.CommitAsync();
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
        }
        }
        catch (Npgsql.PostgresException pgEx) when (pgEx.SqlState == "22P03")
        {
            _logger?.LogError(pgEx, "Database error caching bars for {Symbol} {TimeFrame}: {SqlState}: {Message}",
                symbol, timeFrame, pgEx.SqlState, pgEx.MessageText);

            // Log the specific bar data that caused the issue for debugging
            _logger?.LogError("Binary format error - first few bars for {Symbol}:", symbol);
            foreach (var bar in cachedBars.Take(3))
            {
                _logger?.LogError("Bar: Time={Time:yyyy-MM-dd HH:mm:ss}, Open={Open}, High={High}, Low={Low}, Close={Close}, Volume={Volume}",
                    bar.TimeUtc, bar.Open, bar.High, bar.Low, bar.Close, bar.Volume);
            }

            // Don't throw - let the system continue without caching
            _logger?.LogWarning("Continuing without caching for {Symbol} {TimeFrame} due to PostgreSQL binary format error", symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Database error caching bars for {Symbol} {TimeFrame}: {ErrorMessage}", symbol, timeFrame, ex.Message);

            // Don't throw - let the system continue without caching
            _logger?.LogWarning("Continuing without caching for {Symbol} {TimeFrame} due to database error", symbol, timeFrame);
        }
    }

    /// <summary>
    /// Internal method to add or update cached bars without transaction management
    /// </summary>
    private async Task AddOrUpdateCachedBarsWithoutTransactionAsync(string symbol, string timeFrame, List<CachedStockBar> cachedBars)
    {
        // Bulk check for existing bars to minimize database round trips
        var timeStamps = cachedBars.Select(b => b.TimeUtc).ToList();
        var existingBars = await CachedStockBars
            .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame && timeStamps.Contains(b.TimeUtc))
            .ToDictionaryAsync(b => b.TimeUtc, b => b);

        var newBars = new List<CachedStockBar>();
        var updatedBars = new List<CachedStockBar>();

        foreach (var bar in cachedBars)
        {
            if (existingBars.TryGetValue(bar.TimeUtc, out var existing))
            {
                // Update existing bar with latest data
                existing.Open = bar.Open;
                existing.High = bar.High;
                existing.Low = bar.Low;
                existing.Close = bar.Close;
                existing.Volume = bar.Volume;
                existing.Vwap = bar.Vwap;
                existing.TradeCount = bar.TradeCount;
                existing.CachedAt = DateTime.UtcNow;
                updatedBars.Add(existing);
            }
            else
            {
                newBars.Add(bar);
            }
        }

        // Bulk insert new bars using EFCore.BulkExtensions for ~5x performance improvement
        if (newBars.Any())
        {
            await this.BulkInsertAsync(newBars);
        }

        // Update metadata efficiently
        await UpdateCacheMetadataAsync(symbol, timeFrame, cachedBars);

        // Save changes with retry logic to handle concurrent access
        await SaveChangesWithRetryAsync();
    }

    /// <summary>
    /// Saves changes with retry logic to handle concurrent database access
    /// </summary>
    private async Task SaveChangesWithRetryAsync()
    {
        const int maxRetries = 3;
        const int baseDelayMs = 50;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                await SaveChangesAsync();
                return; // Success
            }
            catch (Exception ex) when (attempt < maxRetries &&
                (ex.Message.Contains("database is locked") ||
                 ex.Message.Contains("UNIQUE constraint failed") ||
                 ex.Message.Contains("timeout")))
            {
                // Exponential backoff with jitter
                var delay = baseDelayMs * (int)Math.Pow(2, attempt - 1) + Random.Shared.Next(0, 25);
                await Task.Delay(delay);
            }
        }

        // Final attempt without catching exceptions
        await SaveChangesAsync();
    }

    /// <summary>
    /// Efficiently updates cache metadata for a symbol and timeframe
    /// </summary>
    private async Task UpdateCacheMetadataAsync(string symbol, string timeFrame, List<CachedStockBar> newBars)
    {
        var cacheKey = Models.StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var latestDate = newBars.Max(b => b.TimeUtc);
        var earliestDate = newBars.Min(b => b.TimeUtc);
        var metadata = await this.StockCacheMetadata.FindAsync(cacheKey);

        if (metadata == null)
        {
            metadata = new StockCacheMetadata
            {
                CacheKey = cacheKey,
                Symbol = symbol,
                TimeFrame = timeFrame,
                LatestDataDate = latestDate,
                EarliestDataDate = earliestDate,
                LastUpdated = DateTime.UtcNow,
                BarCount = newBars.Count
            };
            this.StockCacheMetadata.Add(metadata);
        }
        else
        {
            // Update date ranges
            if (latestDate > metadata.LatestDataDate)
            {
                metadata.LatestDataDate = latestDate;
            }
            if (earliestDate < metadata.EarliestDataDate)
            {
                metadata.EarliestDataDate = earliestDate;
            }
            metadata.LastUpdated = DateTime.UtcNow;

            // Use more efficient count query
            metadata.BarCount = await CachedStockBars
                .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame)
                .CountAsync();
        }
    }

    /// <summary>
    /// Bulk insert operation for multiple symbols and timeframes
    /// </summary>
    public async Task BulkInsertBarsAsync(IDictionary<string, IDictionary<string, IEnumerable<IBar>>> symbolTimeFrameBars)
    {
        // Check if we're using in-memory database (transactions not supported)
        var isInMemory = Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

        if (isInMemory)
        {
            foreach (var symbolEntry in symbolTimeFrameBars)
            {
                var symbol = symbolEntry.Key;
                foreach (var timeFrameEntry in symbolEntry.Value)
                {
                    var timeFrame = timeFrameEntry.Key;
                    var bars = timeFrameEntry.Value;

                    await AddOrUpdateCachedBarsAsync(symbol, timeFrame, bars);
                }
            }
        }
        else
        {
            using var transaction = await Database.BeginTransactionAsync();
            try
            {
                foreach (var symbolEntry in symbolTimeFrameBars)
                {
                    var symbol = symbolEntry.Key;
                    foreach (var timeFrameEntry in symbolEntry.Value)
                    {
                        var timeFrame = timeFrameEntry.Key;
                        var bars = timeFrameEntry.Value;

                        // Call the internal method directly to avoid nested transaction
                        var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();
                        if (cachedBars.Any())
                        {
                            await AddOrUpdateCachedBarsWithoutTransactionAsync(symbol, timeFrame, cachedBars);
                        }
                    }
                }

                await SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }

    /// <summary>
    /// Cleans up old cached data (older than specified days) with optimized bulk operations
    /// </summary>
    public async Task CleanupOldDataAsync(int retainDays = 365)
    {
        // Check if we're using in-memory database (transactions not supported)
        var isInMemory = Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

        if (isInMemory)
        {
            await CleanupOldDataWithoutTransactionAsync(retainDays);
        }
        else
        {
            using var transaction = await Database.BeginTransactionAsync();
            try
            {
                await CleanupOldDataWithoutTransactionAsync(retainDays);
                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }

    /// <summary>
    /// Internal method to cleanup old data without transaction management
    /// </summary>
    private async Task CleanupOldDataWithoutTransactionAsync(int retainDays)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);

        // Check if we're using in-memory database (ExecuteDeleteAsync not supported)
        var isInMemory = Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

        int deletedCount;
        if (isInMemory)
        {
            // For in-memory database, use traditional approach
            var oldBars = await CachedStockBars
                .Where(b => b.TimeUtc < cutoffDate)
                .ToListAsync();

            CachedStockBars.RemoveRange(oldBars);
            deletedCount = oldBars.Count;
        }
        else
        {
            // Use ExecuteDeleteAsync for better performance (EF Core 7+)
            deletedCount = await CachedStockBars
                .Where(b => b.TimeUtc < cutoffDate)
                .ExecuteDeleteAsync();
        }

        // Update metadata after cleanup using bulk operations
        var affectedSymbols = await CachedStockBars
            .Select(b => new { b.Symbol, b.TimeFrame })
            .Distinct()
            .ToListAsync();

        var metadataUpdates = new List<StockCacheMetadata>();
        var metadataToRemove = new List<StockCacheMetadata>();

        foreach (var symbolTimeFrame in affectedSymbols)
        {
            var cacheKey = Models.StockCacheMetadata.CreateCacheKey(symbolTimeFrame.Symbol, symbolTimeFrame.TimeFrame);
            var metadata = await this.StockCacheMetadata.FindAsync(cacheKey);

            if (metadata != null)
            {
                // Use aggregation queries for better performance
                var stats = await CachedStockBars
                    .Where(b => b.Symbol == symbolTimeFrame.Symbol && b.TimeFrame == symbolTimeFrame.TimeFrame)
                    .GroupBy(b => 1)
                    .Select(g => new
                    {
                        Count = g.Count(),
                        MinDate = g.Min(b => b.TimeUtc),
                        MaxDate = g.Max(b => b.TimeUtc)
                    })
                    .FirstOrDefaultAsync();

                if (stats != null && stats.Count > 0)
                {
                    metadata.EarliestDataDate = stats.MinDate;
                    metadata.LatestDataDate = stats.MaxDate;
                    metadata.BarCount = stats.Count;
                    metadata.LastUpdated = DateTime.UtcNow;
                    metadataUpdates.Add(metadata);
                }
                else
                {
                    // No bars left, remove metadata
                    metadataToRemove.Add(metadata);
                }
            }
        }

        // Bulk remove empty metadata
        if (metadataToRemove.Any())
        {
            this.StockCacheMetadata.RemoveRange(metadataToRemove);
        }

        await SaveChangesAsync();
    }

    /// <summary>
    /// Gets cache statistics for monitoring
    /// </summary>
    public async Task<IDictionary<string, Services.CacheStats>> GetCacheStatsAsync()
    {
        var stats = new Dictionary<string, Services.CacheStats>();

        var metadataList = await StockCacheMetadata.ToListAsync();

        foreach (var metadata in metadataList)
        {
            // Estimate size (rough calculation)
            var estimatedSizeBytes = metadata.BarCount * 100; // Rough estimate: 100 bytes per bar

            var cacheStats = new Services.CacheStats(
                metadata.Symbol,
                metadata.TimeFrame,
                metadata.BarCount,
                metadata.EarliestDataDate,
                metadata.LatestDataDate,
                metadata.LastUpdated,
                estimatedSizeBytes
            );

            stats[metadata.CacheKey] = cacheStats;
        }

        return stats;
    }

    /// <summary>
    /// Gets the latest trailing stop record for a symbol
    /// </summary>
    public async Task<TrailingStopRecord?> GetLatestTrailingStopAsync(string symbol)
    {
        return await TrailingStops
            .Where(t => t.Symbol == symbol && t.IsActive)
            .OrderByDescending(t => t.Date)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Gets the latest trailing stops for multiple symbols in a single query (OPTIMIZED)
    /// </summary>
    public async Task<Dictionary<string, TrailingStopRecord?>> GetLatestTrailingStopsAsync(IEnumerable<string> symbols)
    {
        var symbolList = symbols.ToList();
        if (!symbolList.Any()) return new Dictionary<string, TrailingStopRecord?>();

        // Use window function to get latest stop per symbol in single query
        var latestStops = await TrailingStops
            .Where(t => symbolList.Contains(t.Symbol) && t.IsActive)
            .GroupBy(t => t.Symbol)
            .Select(g => g.OrderByDescending(t => t.Date).First())
            .ToListAsync();

        // Create dictionary with all requested symbols (null for missing ones)
        var result = symbolList.ToDictionary(symbol => symbol, symbol => (TrailingStopRecord?)null);
        foreach (var stop in latestStops)
        {
            result[stop.Symbol] = stop;
        }

        return result;
    }

    /// <summary>
    /// Gets trailing stop records for multiple symbols within a date range
    /// </summary>
    public async Task<List<TrailingStopRecord>> GetTrailingStopsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
    {
        var symbolList = symbols.ToList();
        return await TrailingStops
            .Where(t => symbolList.Contains(t.Symbol) && t.Date >= startDate && t.Date <= endDate)
            .OrderBy(t => t.Symbol)
            .ThenByDescending(t => t.Date)
            .ToListAsync();
    }

    /// <summary>
    /// Adds or updates a trailing stop record
    /// </summary>
    public async Task AddOrUpdateTrailingStopAsync(TrailingStopRecord trailingStop)
    {
        var existing = await TrailingStops
            .FirstOrDefaultAsync(t => t.Symbol == trailingStop.Symbol && t.Date.Date == trailingStop.Date.Date);

        if (existing != null)
        {
            // Update existing record
            existing.StopPrice = trailingStop.StopPrice;
            existing.EntryPrice = trailingStop.EntryPrice;
            existing.Atr = trailingStop.Atr;
            existing.HighWaterMark = trailingStop.HighWaterMark;
            existing.Quantity = trailingStop.Quantity;
            existing.EntryDate = trailingStop.EntryDate;
            existing.OrderId = trailingStop.OrderId;
            existing.CreatedAt = DateTime.UtcNow;
            existing.IsActive = trailingStop.IsActive;
        }
        else
        {
            // Add new record
            TrailingStops.Add(trailingStop);
        }

        await SaveChangesAsync();
    }

    /// <summary>
    /// Marks trailing stops as inactive for closed positions
    /// </summary>
    public async Task DeactivateTrailingStopsAsync(string symbol)
    {
        var activeStops = await TrailingStops
            .Where(t => t.Symbol == symbol && t.IsActive)
            .ToListAsync();

        foreach (var stop in activeStops)
        {
            stop.IsActive = false;
        }

        await SaveChangesAsync();
    }

    /// <summary>
    /// PostgreSQL-optimized bulk upsert using ON CONFLICT for maximum performance
    /// Replaces individual INSERT/UPDATE operations with single UPSERT statements
    /// </summary>
    public async Task BulkUpsertBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();

        if (!cachedBars.Any())
            return;

        // Check if we're using PostgreSQL
        if (Database.ProviderName != "Npgsql.EntityFrameworkCore.PostgreSQL")
        {
            // Fallback to standard method for non-PostgreSQL databases
            await AddOrUpdateCachedBarsAsync(symbol, timeFrame, bars);
            return;
        }

        // Use PostgreSQL UPSERT for maximum performance (10x faster than individual operations)
        var sql = @"
            INSERT INTO ""CachedStockBars"" (""Symbol"", ""TimeFrame"", ""TimeUtc"", ""Open"", ""High"", ""Low"", ""Close"", ""Volume"", ""Vwap"", ""TradeCount"", ""CachedAt"")
            VALUES (@Symbol, @TimeFrame, @TimeUtc, @Open, @High, @Low, @Close, @Volume, @Vwap, @TradeCount, @CachedAt)
            ON CONFLICT (""Symbol"", ""TimeFrame"", ""TimeUtc"")
            DO UPDATE SET
                ""Open"" = EXCLUDED.""Open"",
                ""High"" = EXCLUDED.""High"",
                ""Low"" = EXCLUDED.""Low"",
                ""Close"" = EXCLUDED.""Close"",
                ""Volume"" = EXCLUDED.""Volume"",
                ""Vwap"" = EXCLUDED.""Vwap"",
                ""TradeCount"" = EXCLUDED.""TradeCount"",
                ""CachedAt"" = EXCLUDED.""CachedAt""";

        using var connection = Database.GetDbConnection();
        if (connection.State != System.Data.ConnectionState.Open)
            await connection.OpenAsync();

        using var transaction = await connection.BeginTransactionAsync();
        try
        {
            foreach (var bar in cachedBars)
            {
                using var command = connection.CreateCommand();
                command.CommandText = sql;
                command.Transaction = transaction;

                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Symbol", bar.Symbol));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@TimeFrame", bar.TimeFrame));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@TimeUtc", bar.TimeUtc));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Open", bar.Open));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@High", bar.High));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Low", bar.Low));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Close", bar.Close));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Volume", bar.Volume));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@Vwap", bar.Vwap ?? (object)DBNull.Value));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@TradeCount", bar.TradeCount ?? (object)DBNull.Value));
                command.Parameters.Add(new Npgsql.NpgsqlParameter("@CachedAt", bar.CachedAt));

                await command.ExecuteNonQueryAsync();
            }

            await transaction.CommitAsync();
            _logger?.LogDebug("Successfully bulk upserted {Count} bars for {Symbol} {TimeFrame}", cachedBars.Count, symbol, timeFrame);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger?.LogError(ex, "Error in PostgreSQL bulk upsert for {Symbol} {TimeFrame}", symbol, timeFrame);
            throw;
        }
    }
}