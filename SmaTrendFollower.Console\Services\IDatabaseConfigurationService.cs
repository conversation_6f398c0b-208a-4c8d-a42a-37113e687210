using Microsoft.EntityFrameworkCore;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for configuring database connections with optimized settings for performance
/// </summary>
public interface IDatabaseConfigurationService
{
    /// <summary>
    /// Configures PostgreSQL DbContext options with optimized settings
    /// </summary>
    /// <param name="options">DbContext options builder</param>
    /// <param name="connectionString">PostgreSQL connection string</param>
    void ConfigurePostgreSQL(DbContextOptionsBuilder options, string connectionString);

    /// <summary>
    /// Gets optimized PostgreSQL connection string with performance settings
    /// </summary>
    /// <param name="baseConnectionString">Base PostgreSQL connection string</param>
    /// <returns>Optimized connection string</returns>
    string GetOptimizedPostgreSQLConnectionString(string baseConnectionString);

    /// <summary>
    /// Optimizes PostgreSQL database with VACUUM, ANALYZE, and other maintenance operations
    /// </summary>
    /// <param name="connectionString">PostgreSQL connection string</param>
    Task OptimizePostgreSQLAsync(string connectionString);
}
